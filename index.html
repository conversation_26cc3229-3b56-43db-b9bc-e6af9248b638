<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>底部滑动卡片和动态背景</title>

    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />

    <style>
        /* 基础页面样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            display: flex;
            justify-content: center;

            /* --- 修改点 1：将卡片容器对齐到底部 --- */
            align-items: flex-end;

            height: 100vh;
            margin: 0;
            overflow: hidden;

            /* --- 修改点 2：在底部增加一些内边距，让卡片不会紧贴边缘 --- */
            padding-bottom: 40px;
            box-sizing: border-box; /* 确保内边距被正确计算 */
        }

        /* 背景容器样式 */
        .background-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background-size: cover;
            background-position: center;

            /* --- 修改点 3：移除模糊效果，只保留亮度调整 --- */
            filter: brightness(0.7);

            transition: background-image 0.7s ease-in-out;
        }

        /* Swiper 容器样式 */
        .swiper-container {
            width: 100%;
            max-width: 400px;
            /* 移除上下的 padding，因为位置由 body 控制 */
            padding: 15px 20px;
        }

        /* 单个卡片（幻灯片）的样式 */
        .swiper-slide {
            width: 120px;
            height: 90px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: transform 0.4s ease, opacity 0.4s ease;
            transform: scale(0.9);
            opacity: 0.8;
        }

        /* 当前活动的卡片样式 */
        .swiper-slide-active {
            transform: scale(1.1);
            opacity: 1;
        }

        /* 卡片内部内容的样式 */
        .card {
            width: 100%;
            height: 100%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            overflow: hidden;
            text-align: center;
            background-color: transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.4s ease;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 活动卡片的高亮边框样式 */
        .swiper-slide-active .card {
            border-color: #00aaff;
            border-width: 3px;
            box-shadow: 0 4px 12px rgba(0, 170, 255, 0.4);
        }

        .card img {
            width: 100%;
            height: 60px;
            object-fit: cover;
            border-radius: 6px 6px 0 0;
        }

        .card p {
            margin: 0;
            padding: 4px 6px;
            font-size: 12px;
            font-weight: 500;
            color: white;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0 0 6px 6px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }
    </style>
</head>
<body>

    <div class="background-container"></div>

    <div class="swiper-container my-slider">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <div class="card">
            <img src="https://picsum.photos/id/1043/400/300" alt="大水车">
            <p>大水车</p>
          </div>
        </div>
        <div class="swiper-slide">
          <div class="card">
            <img src="https://picsum.photos/id/10/400/300" alt="古镇广场">
            <p>古镇广场</p>
          </div>
        </div>
        <div class="swiper-slide">
          <div class="card">
            <img src="https://picsum.photos/id/1015/400/300" alt="林间小路">
            <p>林间小路</p>
          </div>
        </div>
        <div class="swiper-slide">
            <div class="card">
              <img src="https://picsum.photos/id/1018/400/300" alt="雪山之巅">
              <p>雪山之巅</p>
            </div>
        </div>
        <div class="swiper-slide">
            <div class="card">
              <img src="https://picsum.photos/id/1025/400/300" alt="可爱宠物">
              <p>可爱宠物</p>
            </div>
        </div>
        <div class="swiper-slide">
            <div class="card">
              <img src="https://picsum.photos/id/106/400/300" alt="城市夜景">
              <p>城市夜景</p>
            </div>
        </div>
      </div>
    </div>

    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>

    <script>
      document.addEventListener('DOMContentLoaded', function () {

        const backgroundContainer = document.querySelector('.background-container');

        const swiper = new Swiper('.my-slider', {
          slidesPerView: 'auto',
          spaceBetween: 10,
          centeredSlides: true,
          loop: true,
          on: {
            init: function () {
              updateBackground(this);
            },
            slideChange: function () {
              updateBackground(this);
            },
          },
        });

        function updateBackground(swiperInstance) {
          const activeSlide = swiperInstance.slides[swiperInstance.activeIndex];
          const image = activeSlide.querySelector('img');

          if (image) {
            const imageUrl = image.src;
            backgroundContainer.style.backgroundImage = `url(${imageUrl})`;
          }
        }
      });
    </script>

</body>
</html>