<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>底部滑动卡片和动态背景</title>

    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />

    <style>
        /* 基础页面样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            position: relative;
            width: 100%;
        }

        html {
            overflow-x: hidden;
            width: 100%;
        }

        /* 背景容器样式 */
        .background-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background-size: cover;
            background-position: center;
            transition: background-image 0.7s ease-in-out;
        }

        /* 顶部导航栏 */
        .top-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-title {
            color: white;
            font-size: 18px;
            font-weight: 500;
            margin-left: 15px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
        }

        /* 右侧功能按钮 */
        .side-buttons {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 999;
        }

        .side-btn {
            width: 44px;
            height: 44px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .side-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        /* 场景按钮特殊样式 */
        .scene-btn {
            background: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            font-weight: bold;
        }

        .scene-btn.active {
            background: #00aaff;
            color: white;
        }

        /* 底部卡片容器 */
        .bottom-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 998;
            transform: translateY(0);
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s ease;
            opacity: 1;
            overflow: hidden;
            width: 100%;
        }

        /* 隐藏状态 */
        .bottom-container.hidden {
            transform: translateY(100%);
            opacity: 0;
        }

        /* 加载动画 */
        .bottom-container.loading {
            animation: slideUpFade 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        @keyframes slideUpFade {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Swiper 容器样式 */
        .swiper-container {
            width: 100%;
            padding: 15px 20px 30px;
            overflow: hidden;
            box-sizing: border-box;
        }

        /* Swiper wrapper 样式 */
        .swiper-wrapper {
            box-sizing: border-box;
        }

        /* 单个卡片（幻灯片）的样式 */
        .swiper-slide {
            width: 120px;
            height: 90px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: transform 0.4s ease, opacity 0.4s ease;
            transform: scale(0.9);
            opacity: 0.8;
            flex-shrink: 0;
            box-sizing: border-box;
        }

        /* 当前活动的卡片样式 */
        .swiper-slide-active {
            transform: scale(1.1);
            opacity: 1;
        }

        /* 卡片内部内容的样式 */
        .card {
            width: 100%;
            height: 100%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            overflow: hidden;
            text-align: center;
            background-color: transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.4s ease;
            display: flex;
            flex-direction: column;
            position: relative;
            box-sizing: border-box;
        }

        /* 活动卡片的高亮边框样式 */
        .swiper-slide-active .card {
            border-color: #00aaff;
            border-width: 3px;
            box-shadow: 0 4px 12px rgba(0, 170, 255, 0.4);
        }

        .card img {
            width: 100%;
            height: 60px;
            object-fit: cover;
            border-radius: 6px 6px 0 0;
        }

        .card p {
            margin: 0;
            padding: 4px 6px;
            font-size: 12px;
            font-weight: 500;
            color: white;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0 0 6px 6px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }


    </style>
</head>
<body>
    <!-- 背景容器 -->
    <div class="background-container"></div>

    <!-- 顶部导航栏 -->
    <div class="top-nav">
        <div class="nav-left">
            <button class="nav-btn">‹</button>
            <div class="nav-title">丽江古城</div>
        </div>
        <div class="nav-right">
            <button class="nav-btn">⋯</button>
            <button class="nav-btn">▶</button>
        </div>
    </div>

    <!-- 右侧功能按钮 -->
    <div class="side-buttons">
        <button class="side-btn">👤</button>
        <button class="side-btn">🔗</button>
        <button class="side-btn scene-btn" id="sceneToggle">场景</button>
    </div>

    <!-- 底部卡片容器 -->
    <div class="bottom-container">
        <div class="swiper-container my-slider">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <div class="card">
                        <img src="https://picsum.photos/id/1043/400/300" alt="大水车">
                        <p>大水车</p>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <img src="https://picsum.photos/id/10/400/300" alt="古镇广场">
                        <p>古镇广场</p>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <img src="https://picsum.photos/id/1015/400/300" alt="林间小路">
                        <p>林间小路</p>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <img src="https://picsum.photos/id/1018/400/300" alt="雪山之巅">
                        <p>雪山之巅</p>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <img src="https://picsum.photos/id/1025/400/300" alt="可爱宠物">
                        <p>可爱宠物</p>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <img src="https://picsum.photos/id/106/400/300" alt="城市夜景">
                        <p>城市夜景</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>

    <script>
      document.addEventListener('DOMContentLoaded', function () {

        const backgroundContainer = document.querySelector('.background-container');
        const bottomContainer = document.querySelector('.bottom-container');
        const sceneToggle = document.getElementById('sceneToggle');
        let isNavigationVisible = true;

        const swiper = new Swiper('.my-slider', {
          slidesPerView: 'auto',
          spaceBetween: 10,
          centeredSlides: true,
          loop: true,
          on: {
            init: function () {
              updateBackground(this);
            },
            slideChange: function () {
              updateBackground(this);
            },
          },
        });

        function updateBackground(swiperInstance) {
          const activeSlide = swiperInstance.slides[swiperInstance.activeIndex];
          const image = activeSlide.querySelector('img');

          if (image) {
            const imageUrl = image.src;
            backgroundContainer.style.backgroundImage = `url(${imageUrl})`;
          }
        }

        // 场景按钮点击事件
        sceneToggle.addEventListener('click', function() {
          if (isNavigationVisible) {
            // 隐藏导航
            bottomContainer.classList.add('hidden');
            sceneToggle.classList.add('active');
            sceneToggle.textContent = '导航';
            isNavigationVisible = false;
          } else {
            // 显示导航
            bottomContainer.classList.remove('hidden');
            bottomContainer.classList.add('loading');
            sceneToggle.classList.remove('active');
            sceneToggle.textContent = '场景';
            isNavigationVisible = true;

            // 移除加载动画类
            setTimeout(() => {
              bottomContainer.classList.remove('loading');
            }, 500);
          }
        });

        // 初始加载动画
        setTimeout(() => {
          bottomContainer.classList.add('loading');
          setTimeout(() => {
            bottomContainer.classList.remove('loading');
          }, 500);
        }, 300);
      });
    </script>

</body>
</html>